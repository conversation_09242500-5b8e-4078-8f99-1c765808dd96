import { NextRequest, NextResponse } from 'next/server';
import { getUserByUsername, updateUserPassword, getVerificationCode, markVerificationCodeAsUsed } from '@/lib/database';
import bcrypt from 'bcryptjs';

/**
 * POST /api/auth/reset-password-email - Reset user password using email verification code (for regular users)
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, email, verificationCode, newPassword } = body;
    
    // Validate required fields
    if (!username || !email || !verificationCode || !newPassword) {
      return NextResponse.json(
        { error: 'Username, email, verification code, and new password are required' },
        { status: 400 }
      );
    }
    
    // Get user from database
    const user = await getUserByUsername(username);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Ensure user is regular type
    if (user.role !== 'regular') {
      return NextResponse.json(
        { error: 'Password reset via email is only available for regular users' },
        { status: 400 }
      );
    }
    
    // Verify email matches user account
    if (!user.gmail || user.gmail !== email) {
      return NextResponse.json(
        { error: 'Email does not match the registered email for this user' },
        { status: 400 }
      );
    }
    
    // Verify the email verification code
    const verificationRecord = await getVerificationCode(email, verificationCode, 'password-reset');
    
    if (!verificationRecord) {
      return NextResponse.json(
        { error: 'Invalid or expired verification code' },
        { status: 400 }
      );
    }
    
    // Validate new password strength
    if (newPassword.length < 6) {
      return NextResponse.json(
        { error: 'New password must be at least 6 characters long' },
        { status: 400 }
      );
    }
    
    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    // Update the user's password
    await updateUserPassword(user.id, hashedPassword);
    
    // Mark verification code as used
    await markVerificationCodeAsUsed(verificationRecord.id);
    
    return NextResponse.json(
      { 
        message: 'Password reset successful',
        user: {
          id: user.id,
          username: user.username
        }
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error during password reset:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
