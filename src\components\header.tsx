"use client";

import { useState } from "react";
import Image from "next/image";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ThemeToggle } from "@/components/theme-toggle";
import { NotificationButton } from "@/components/notification-button";
import { UserAuthDialog } from "@/components/user-auth-dialog";
import { useAdminAuth, useUserAuth } from "@/hooks/use-local-storage";
import { User, LogIn, LogOut } from "lucide-react";

export function Header() {
  const { adminMode } = useAdminAuth();
  const { user, isAuthenticated, logout } = useUserAuth();
  const [showUserAuthDialog, setShowUserAuthDialog] = useState(false);

  const handleUserAuthSuccess = () => {
    setShowUserAuthDialog(false);
  };

  const handleUserLogout = () => {
    logout();
  };

  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <div className="flex flex-1 items-center justify-between">
          <div className="flex items-center gap-3">
            <Image
              src="/images/LDIS.png"
              alt="LDIS Logo"
              width={32}
              height={32}
              className="h-8 w-8"
            />
            <h1 className="text-lg font-semibold">LDIS</h1>
          </div>
          <div className="flex items-center gap-2">
            {adminMode && <NotificationButton />}

            {/* User Authentication Section */}
            {isAuthenticated && user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <User className="h-4 w-4" />
                    <span className="hidden sm:inline">{user.username}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>User Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <div className="px-2 py-1.5 text-sm">
                    <div className="font-medium">{user.username}</div>
                    <div className="text-xs text-muted-foreground capitalize">
                      {user.role} User
                    </div>
                    {user.gmail && (
                      <div className="text-xs text-muted-foreground">
                        {user.gmail}
                      </div>
                    )}
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleUserLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowUserAuthDialog(true)}
                className="flex items-center gap-2"
              >
                <LogIn className="h-4 w-4" />
                <span className="hidden sm:inline">Sign In</span>
              </Button>
            )}

            <ThemeToggle />
          </div>
        </div>
      </header>

      <UserAuthDialog
        open={showUserAuthDialog}
        onClose={() => setShowUserAuthDialog(false)}
        onSuccess={handleUserAuthSuccess}
      />
    </>
  );
}
