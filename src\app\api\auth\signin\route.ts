import { NextRequest, NextResponse } from 'next/server';
import { getUserByUsername, getVerificationCode, markVerificationCodeAsUsed } from '@/lib/database';
import bcrypt from 'bcryptjs';

/**
 * POST /api/auth/signin - Authenticate user
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password, verificationCode } = body;
    
    // Validate required fields
    if (!username || !password) {
      return NextResponse.json(
        { error: 'Username and password are required' },
        { status: 400 }
      );
    }
    
    // Get user from database
    const user = await getUserByUsername(username);
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid username or password' },
        { status: 401 }
      );
    }
    
    // Verify password
    const passwordMatch = await bcrypt.compare(password, user.password);
    if (!passwordMatch) {
      return NextResponse.json(
        { error: 'Invalid username or password' },
        { status: 401 }
      );
    }

    // For regular users, verify email verification code
    if (user.role === 'regular') {
      if (!verificationCode) {
        return NextResponse.json(
          { error: 'Email verification code is required for regular users' },
          { status: 400 }
        );
      }

      if (!user.gmail) {
        return NextResponse.json(
          { error: 'No email address found for this user' },
          { status: 400 }
        );
      }

      const verificationRecord = await getVerificationCode(user.gmail, verificationCode, 'signin');

      if (!verificationRecord) {
        return NextResponse.json(
          { error: 'Invalid or expired verification code' },
          { status: 400 }
        );
      }

      // Mark verification code as used
      await markVerificationCodeAsUsed(verificationRecord.id);
    }

    // Authentication successful
    return NextResponse.json(
      { 
        message: 'Sign in successful',
        user: {
          id: user.id,
          username: user.username,
          role: user.role,
          gmail: user.gmail,
          created_at: user.created_at
        }
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error during sign in:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
