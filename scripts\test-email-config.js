/**
 * Test script for email configuration
 * This script tests the email system configuration
 */

// Load environment variables manually
const fs = require("fs");
const path = require("path");

try {
  const envPath = path.join(__dirname, "..", ".env.local");
  const envContent = fs.readFileSync(envPath, "utf8");
  const envLines = envContent.split("\n");

  envLines.forEach((line) => {
    const trimmedLine = line.trim();
    if (trimmedLine && !trimmedLine.startsWith("#")) {
      const [key, ...valueParts] = trimmedLine.split("=");
      if (key && valueParts.length > 0) {
        process.env[key.trim()] = valueParts.join("=").trim();
      }
    }
  });
} catch (error) {
  console.log("Could not load .env.local file:", error.message);
}

async function testEmailConfiguration() {
  console.log("🧪 Testing Email Configuration...\n");

  // Check environment variables
  console.log("📋 Environment Variables:");
  console.log(
    `   GMAIL_USER: ${process.env.GMAIL_USER ? "✅ Set" : "❌ Not set"}`
  );
  console.log(
    `   GMAIL_APP_PASSWORD: ${
      process.env.GMAIL_APP_PASSWORD ? "✅ Set" : "❌ Not set"
    }`
  );

  if (!process.env.GMAIL_USER || !process.env.GMAIL_APP_PASSWORD) {
    console.log("\n❌ Gmail credentials not configured properly");
    return;
  }

  console.log(`\n📧 Gmail User: ${process.env.GMAIL_USER}`);
  console.log(
    `📧 App Password: ${process.env.GMAIL_APP_PASSWORD.substring(0, 4)}****`
  );

  try {
    // Test nodemailer import and configuration
    const nodemailer = require("nodemailer");
    console.log("\n✅ Nodemailer imported successfully");

    const config = {
      service: "gmail",
      host: "smtp.gmail.com",
      port: 587,
      secure: false,
      auth: {
        user: process.env.GMAIL_USER,
        pass: process.env.GMAIL_APP_PASSWORD,
      },
    };

    console.log("\n📝 Creating transporter...");
    const transporter = nodemailer.createTransport(config);
    console.log("✅ Transporter created successfully");

    console.log("\n🔍 Verifying transporter...");
    await transporter.verify();
    console.log("✅ Email configuration verified successfully!");

    console.log("\n🎉 Email system is ready to use!");
  } catch (error) {
    console.error("\n❌ Email configuration test failed:", error.message);

    if (error.message.includes("Invalid login")) {
      console.log("\n💡 Troubleshooting tips:");
      console.log(
        "   1. Make sure 2-Factor Authentication is enabled on your Gmail account"
      );
      console.log(
        "   2. Generate a new App Password from Google Account settings"
      );
      console.log(
        "   3. Use the App Password (not your regular Gmail password)"
      );
      console.log("   4. Make sure the Gmail address is correct");
    }
  }
}

// Run the test
testEmailConfiguration();
