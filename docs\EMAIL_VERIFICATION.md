# Email Verification System for LDIS

## Overview

The LDIS (Legal Document Issuance System) now includes email verification for regular users to enhance security. This feature requires regular users to verify their Gmail address during both signup and signin processes.

## Features

- **Email verification for regular users only** - Admin users can still sign up and sign in without email verification
- **Signup verification** - New regular users must verify their email before account creation
- **Signin verification** - Existing regular users must verify their email each time they sign in
- **6-digit verification codes** - Secure, time-limited codes sent via email
- **10-minute expiration** - Codes expire automatically for security
- **Resend functionality** - Users can request new codes if needed
- **Professional email templates** - Branded emails with clear instructions

## Setup Instructions

### 1. Gmail Configuration

To enable email verification, you need to set up Gmail App Password:

1. Go to your Google Account settings (https://myaccount.google.com/)
2. Navigate to Security > 2-Step Verification
3. Enable 2-Step Verification if not already enabled
4. Go to Security > App passwords
5. Generate a new app password for "Mail"
6. Copy the 16-character app password

### 2. Environment Variables

Create a `.env.local` file in your project root with the following variables:

```env
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your-16-character-app-password
```

Example:
```env
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=abcd efgh ijkl mnop
```

### 3. Restart the Application

After setting up the environment variables, restart your development server:

```bash
pnpm dev
```

## How It Works

### For Regular Users

#### Signup Process:
1. User selects "Regular User" role
2. User enters username, password, and Gmail address
3. User clicks "Sign Up" - verification code is sent to their email
4. User enters the 6-digit code from their email
5. User clicks "Sign Up" again to complete registration

#### Signin Process:
1. User enters username and password
2. User clicks "Sign In" - verification code is sent to their registered email
3. User enters the 6-digit code from their email
4. User clicks "Sign In" again to complete authentication

### For Admin Users

Admin users can sign up and sign in normally without any email verification requirements.

## API Endpoints

### Send Verification Code
- **POST** `/api/auth/send-verification`
- **Body**: `{ email: string, type: 'signup' | 'signin', username?: string }`
- **Response**: `{ message: string, email: string, expiresIn: number }`

### Verify Code
- **POST** `/api/auth/verify-code`
- **Body**: `{ email: string, code: string, type: 'signup' | 'signin' }`
- **Response**: `{ message: string, email: string, verified: boolean }`

### Updated Signup
- **POST** `/api/auth/signup`
- **Body**: `{ username: string, password: string, role: string, gmail?: string, verificationCode?: string }`

### Updated Signin
- **POST** `/api/auth/signin`
- **Body**: `{ username: string, password: string, verificationCode?: string }`

## Database Schema

### New Table: verification_codes

```sql
CREATE TABLE verification_codes (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  email TEXT NOT NULL,
  code TEXT NOT NULL,
  type TEXT NOT NULL, -- 'signup' or 'signin'
  user_id INTEGER,
  expires_at DATETIME NOT NULL,
  used BOOLEAN DEFAULT FALSE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);
```

## Security Features

- **Time-limited codes**: All verification codes expire after 10 minutes
- **Single-use codes**: Codes are marked as used after successful verification
- **Type-specific codes**: Separate codes for signup and signin processes
- **Automatic cleanup**: Expired codes are automatically removed from the database
- **Rate limiting**: Only one active code per email/type combination

## Testing

Run the email verification tests:

```bash
node scripts/test-email-verification.js
```

Clean test data:

```bash
node scripts/clean-test-data.js
```

## Troubleshooting

### Email Not Sending

1. **Check environment variables**: Ensure `GMAIL_USER` and `GMAIL_APP_PASSWORD` are set correctly
2. **Verify Gmail settings**: Make sure 2-Step Verification is enabled and App Password is generated
3. **Check server logs**: Look for email-related errors in the console
4. **Test email configuration**: Use the `testEmailConfiguration()` function in `src/lib/email.ts`

### Verification Code Issues

1. **Code expired**: Codes expire after 10 minutes - request a new one
2. **Code already used**: Each code can only be used once - request a new one
3. **Wrong email**: Make sure you're checking the correct email address
4. **Database issues**: Check if the verification_codes table exists and is accessible

### UI Issues

1. **Verification input not showing**: Make sure you've selected "Regular User" role and entered a valid Gmail address
2. **Countdown not working**: Check browser console for JavaScript errors
3. **Resend button not appearing**: Wait for the countdown to reach zero

## File Structure

```
src/
├── lib/
│   ├── email.ts              # Email service utilities
│   └── database.ts           # Database operations (updated)
├── app/api/auth/
│   ├── send-verification/    # Send verification code endpoint
│   ├── verify-code/          # Verify code endpoint
│   ├── signup/               # Updated signup endpoint
│   └── signin/               # Updated signin endpoint
└── components/
    └── auth-dialog.tsx       # Updated authentication UI

scripts/
├── test-email-verification.js  # Test script
├── clean-test-data.js          # Cleanup script
└── debug-datetime.js           # Debug utilities

docs/
└── EMAIL_VERIFICATION.md      # This documentation
```

## Future Enhancements

- **SMS verification**: Add phone number verification as an alternative
- **Email templates**: Customize email templates for different use cases
- **Rate limiting**: Implement rate limiting for verification code requests
- **Admin dashboard**: Add admin interface to manage verification codes
- **Audit logging**: Track verification attempts for security monitoring
