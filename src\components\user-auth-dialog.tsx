"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { useUserAuth } from "@/hooks/use-local-storage";
import {
  validateUsername,
  validatePassword,
  validateEmail,
} from "@/lib/validation";

interface UserAuthDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function UserAuthDialog({
  open,
  onClose,
  onSuccess,
}: UserAuthDialogProps) {
  const { authenticate } = useUserAuth();
  const [activeTab, setActiveTab] = useState("signin");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [verificationStep, setVerificationStep] = useState<{
    type: "signup" | "signin" | "password-reset" | null;
    email: string;
    username: string;
    countdown: number;
  }>({ type: null, email: "", username: "", countdown: 0 });
  const [verificationLoading, setVerificationLoading] = useState(false);
  const [generatedRecoveryKey, setGeneratedRecoveryKey] = useState("");

  // Form states for regular users only
  const [signInData, setSignInData] = useState({
    username: "",
    password: "",
    verificationCode: "",
  });
  const [signUpData, setSignUpData] = useState({
    username: "",
    password: "",
    gmail: "",
    verificationCode: "",
  });
  const [forgotPasswordData, setForgotPasswordData] = useState({
    username: "",
    email: "",
    verificationCode: "",
    newPassword: "",
  });

  const resetForms = () => {
    setSignInData({ username: "", password: "", verificationCode: "" });
    setSignUpData({
      username: "",
      password: "",
      gmail: "",
      verificationCode: "",
    });
    setForgotPasswordData({
      username: "",
      email: "",
      verificationCode: "",
      newPassword: "",
    });
    setError("");
    setVerificationStep({ type: null, email: "", username: "", countdown: 0 });
    setGeneratedRecoveryKey("");
  };

  const handleClose = () => {
    resetForms();
    onClose();
  };

  const sendVerificationCode = async (
    email: string,
    type: "signup" | "signin" | "password-reset",
    username?: string
  ) => {
    setVerificationLoading(true);
    try {
      const response = await fetch("/api/auth/send-verification", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, type, username }),
      });

      const data = await response.json();

      if (response.ok) {
        setVerificationStep({
          type,
          email,
          username: username || "",
          countdown: 60,
        });
        toast.success(`Verification code sent to ${email}`);

        // Start countdown
        const interval = setInterval(() => {
          setVerificationStep((prev) => {
            if (prev.countdown <= 1) {
              clearInterval(interval);
              return { ...prev, countdown: 0 };
            }
            return { ...prev, countdown: prev.countdown - 1 };
          });
        }, 1000);
      } else {
        toast.error(data.error || "Failed to send verification code");
        setError(data.error || "Failed to send verification code");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setVerificationLoading(false);
    }
  };

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Client-side validation
    const usernameValidation = validateUsername(signInData.username);
    if (!usernameValidation.isValid) {
      setError(usernameValidation.message!);
      setLoading(false);
      return;
    }

    const passwordValidation = validatePassword(signInData.password);
    if (!passwordValidation.isValid) {
      setError(passwordValidation.message!);
      setLoading(false);
      return;
    }

    // Check if user needs email verification (regular users only)
    if (verificationStep.type !== "signin" && signInData.username) {
      try {
        const userCheckResponse = await fetch("/api/auth/signin", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            username: signInData.username,
            password: signInData.password,
          }),
        });

        if (userCheckResponse.status === 400) {
          const userData = await userCheckResponse.json();
          if (
            userData.error ===
            "Email verification code is required for regular users"
          ) {
            // Send verification code for regular user
            await sendVerificationCode("", "signin", signInData.username);
            setLoading(false);
            return;
          }
        }
      } catch (error) {
        console.error("Error checking user type:", error);
      }
    }

    const signinPayload = {
      username: signInData.username,
      password: signInData.password,
      ...(signInData.verificationCode && {
        verificationCode: signInData.verificationCode,
      }),
    };

    try {
      const response = await fetch("/api/auth/signin", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(signinPayload),
      });

      const data = await response.json();

      if (response.ok) {
        // Only authenticate regular users through this dialog
        if (data.user.role === "regular") {
          authenticate({
            id: data.user.id,
            username: data.user.username,
            role: data.user.role,
            gmail: data.user.gmail,
          });
          toast.success("Signed in successfully!");
          onSuccess();
          resetForms();
        } else {
          setError(
            "Admin users should use the admin authentication in Settings."
          );
        }
      } else {
        toast.error(data.error || "Sign in failed");
        setError(data.error || "Sign in failed");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Client-side validation
    const usernameValidation = validateUsername(signUpData.username);
    if (!usernameValidation.isValid) {
      setError(usernameValidation.message!);
      setLoading(false);
      return;
    }

    const passwordValidation = validatePassword(signUpData.password);
    if (!passwordValidation.isValid) {
      setError(passwordValidation.message!);
      setLoading(false);
      return;
    }

    const emailValidation = validateEmail(signUpData.gmail);
    if (!emailValidation.isValid) {
      setError(emailValidation.message!);
      setLoading(false);
      return;
    }

    // Send verification code if not in verification step
    if (verificationStep.type !== "signup") {
      await sendVerificationCode(
        signUpData.gmail,
        "signup",
        signUpData.username
      );
      setLoading(false);
      return;
    }

    if (!signUpData.verificationCode) {
      setError("Please enter the verification code sent to your email");
      setLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          username: signUpData.username,
          password: signUpData.password,
          role: "regular", // Always regular for this dialog
          gmail: signUpData.gmail,
          verificationCode: signUpData.verificationCode,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        if (data.recoveryKey) {
          setGeneratedRecoveryKey(data.recoveryKey);
        }

        authenticate({
          id: data.user.id,
          username: data.user.username,
          role: data.user.role,
          gmail: data.user.gmail,
        });

        toast.success("Account created successfully!");
        onSuccess();
        resetForms();
      } else {
        toast.error(data.error || "Sign up failed");
        setError(data.error || "Sign up failed");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Send verification code if not in verification step
    if (verificationStep.type !== "password-reset") {
      const emailValidation = validateEmail(forgotPasswordData.email);
      if (!emailValidation.isValid) {
        setError(emailValidation.message!);
        setLoading(false);
        return;
      }

      await sendVerificationCode(
        forgotPasswordData.email,
        "password-reset",
        forgotPasswordData.username
      );
      setLoading(false);
      return;
    }

    // Validate all fields for password reset
    if (
      !forgotPasswordData.verificationCode ||
      !forgotPasswordData.newPassword
    ) {
      setError("Please fill in all required fields");
      setLoading(false);
      return;
    }

    const passwordValidation = validatePassword(forgotPasswordData.newPassword);
    if (!passwordValidation.isValid) {
      setError(passwordValidation.message!);
      setLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/auth/forgot-password-email", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          username: forgotPasswordData.username,
          email: forgotPasswordData.email,
          verificationCode: forgotPasswordData.verificationCode,
          newPassword: forgotPasswordData.newPassword,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Password reset successfully!");
        setActiveTab("signin");
        resetForms();
      } else {
        toast.error(data.error || "Password reset failed");
        setError(data.error || "Password reset failed");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>User Authentication</DialogTitle>
          <DialogDescription>
            Sign in or create a regular user account to access LDIS features.
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {generatedRecoveryKey && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p className="font-medium">
                  Your recovery key has been generated:
                </p>
                <code className="block p-2 bg-muted rounded text-sm break-all">
                  {generatedRecoveryKey}
                </code>
                <p className="text-xs">
                  Please save this recovery key in a secure location. You'll
                  need it to recover your account if you forget your password.
                </p>
              </div>
            </AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="signin">Sign In</TabsTrigger>
            <TabsTrigger value="signup">Sign Up</TabsTrigger>
            <TabsTrigger value="forgot">Forgot Password</TabsTrigger>
          </TabsList>

          <TabsContent value="signin" className="space-y-4">
            <form onSubmit={handleSignIn} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="signin-username">Username</Label>
                <Input
                  id="signin-username"
                  type="text"
                  value={signInData.username}
                  onChange={(e) =>
                    setSignInData({ ...signInData, username: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="signin-password">Password</Label>
                <Input
                  id="signin-password"
                  type="password"
                  value={signInData.password}
                  onChange={(e) =>
                    setSignInData({ ...signInData, password: e.target.value })
                  }
                  required
                />
              </div>
              {verificationStep.type === "signin" && (
                <div className="space-y-2">
                  <Label htmlFor="signin-verification">Verification Code</Label>
                  <Input
                    id="signin-verification"
                    type="text"
                    value={signInData.verificationCode}
                    onChange={(e) =>
                      setSignInData({
                        ...signInData,
                        verificationCode: e.target.value,
                      })
                    }
                    placeholder="Enter verification code"
                    required
                  />
                  {verificationStep.countdown > 0 && (
                    <p className="text-xs text-muted-foreground">
                      Resend code in {verificationStep.countdown}s
                    </p>
                  )}
                </div>
              )}
              <Button type="submit" className="w-full" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Sign In
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="signup" className="space-y-4">
            <form onSubmit={handleSignUp} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="signup-username">Username</Label>
                <Input
                  id="signup-username"
                  type="text"
                  value={signUpData.username}
                  onChange={(e) =>
                    setSignUpData({ ...signUpData, username: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="signup-password">Password</Label>
                <Input
                  id="signup-password"
                  type="password"
                  value={signUpData.password}
                  onChange={(e) =>
                    setSignUpData({ ...signUpData, password: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="signup-email">Email</Label>
                <Input
                  id="signup-email"
                  type="email"
                  value={signUpData.gmail}
                  onChange={(e) =>
                    setSignUpData({ ...signUpData, gmail: e.target.value })
                  }
                  required
                />
              </div>
              {verificationStep.type === "signup" && (
                <div className="space-y-2">
                  <Label htmlFor="signup-verification">Verification Code</Label>
                  <Input
                    id="signup-verification"
                    type="text"
                    value={signUpData.verificationCode}
                    onChange={(e) =>
                      setSignUpData({
                        ...signUpData,
                        verificationCode: e.target.value,
                      })
                    }
                    placeholder="Enter verification code"
                    required
                  />
                  {verificationStep.countdown > 0 && (
                    <p className="text-xs text-muted-foreground">
                      Resend code in {verificationStep.countdown}s
                    </p>
                  )}
                </div>
              )}
              <Button type="submit" className="w-full" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {verificationStep.type !== "signup"
                  ? "Send Verification Code"
                  : "Create Account"}
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="forgot" className="space-y-4">
            <form onSubmit={handleForgotPassword} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="forgot-username">Username</Label>
                <Input
                  id="forgot-username"
                  type="text"
                  value={forgotPasswordData.username}
                  onChange={(e) =>
                    setForgotPasswordData({
                      ...forgotPasswordData,
                      username: e.target.value,
                    })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="forgot-email">Email</Label>
                <Input
                  id="forgot-email"
                  type="email"
                  value={forgotPasswordData.email}
                  onChange={(e) =>
                    setForgotPasswordData({
                      ...forgotPasswordData,
                      email: e.target.value,
                    })
                  }
                  required
                />
              </div>
              {verificationStep.type === "password-reset" && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="forgot-verification">
                      Verification Code
                    </Label>
                    <Input
                      id="forgot-verification"
                      type="text"
                      value={forgotPasswordData.verificationCode}
                      onChange={(e) =>
                        setForgotPasswordData({
                          ...forgotPasswordData,
                          verificationCode: e.target.value,
                        })
                      }
                      placeholder="Enter verification code"
                      required
                    />
                    {verificationStep.countdown > 0 && (
                      <p className="text-xs text-muted-foreground">
                        Resend code in {verificationStep.countdown}s
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="forgot-new-password">New Password</Label>
                    <Input
                      id="forgot-new-password"
                      type="password"
                      value={forgotPasswordData.newPassword}
                      onChange={(e) =>
                        setForgotPasswordData({
                          ...forgotPasswordData,
                          newPassword: e.target.value,
                        })
                      }
                      required
                    />
                  </div>
                </>
              )}
              <Button type="submit" className="w-full" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {verificationStep.type !== "password-reset"
                  ? "Send Verification Code"
                  : "Reset Password"}
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
