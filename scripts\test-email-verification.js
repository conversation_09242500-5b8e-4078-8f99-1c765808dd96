const {
  initializeDatabase,
  createVerificationCode,
  getVerificationCode,
  markVerificationCodeAsUsed,
} = require("../src/lib/database.ts");

async function testEmailVerification() {
  console.log("🧪 Testing Email Verification System...\n");

  try {
    // Initialize database
    console.log("1. Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized\n");

    // Test creating verification code
    console.log("2. Creating verification code...");
    const email = "<EMAIL>";
    const code = "123456";
    const type = "signup";
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000).toISOString(); // 10 minutes from now

    const codeId = await createVerificationCode(email, code, type, expiresAt);
    console.log(`✅ Verification code created with ID: ${codeId}\n`);

    // Test retrieving verification code
    console.log("3. Retrieving verification code...");
    const retrievedCode = await getVerificationCode(email, code, type);

    if (retrievedCode) {
      console.log("✅ Verification code retrieved successfully:");
      console.log(`   - ID: ${retrievedCode.id}`);
      console.log(`   - Email: ${retrievedCode.email}`);
      console.log(`   - Code: ${retrievedCode.code}`);
      console.log(`   - Type: ${retrievedCode.type}`);
      console.log(`   - Used: ${retrievedCode.used}`);
      console.log(`   - Expires At: ${retrievedCode.expires_at}\n`);
    } else {
      console.log("❌ Failed to retrieve verification code\n");
      return;
    }

    // Test marking code as used
    console.log("4. Marking verification code as used...");
    await markVerificationCodeAsUsed(retrievedCode.id);
    console.log("✅ Verification code marked as used\n");

    // Test retrieving used code (should fail)
    console.log("5. Attempting to retrieve used code...");
    const usedCode = await getVerificationCode(email, code, type);

    if (!usedCode) {
      console.log("✅ Used verification code correctly rejected\n");
    } else {
      console.log("❌ Used verification code was incorrectly retrieved\n");
    }

    // Test expired code
    console.log("6. Testing expired code...");
    const expiredCode = "654321";
    const pastExpiresAt = new Date(Date.now() - 60000).toISOString(); // 1 minute ago

    await createVerificationCode(email, expiredCode, type, pastExpiresAt);
    const expiredRetrieved = await getVerificationCode(
      email,
      expiredCode,
      type
    );

    if (!expiredRetrieved) {
      console.log("✅ Expired verification code correctly rejected\n");
    } else {
      console.log("❌ Expired verification code was incorrectly retrieved\n");
    }

    console.log("🎉 All email verification tests passed!");
  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

// Run the test
testEmailVerification();
